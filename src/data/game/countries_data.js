// List of aliases for every countries and terrritories
// Not stored in a JSON, as it is intended to be unchanged
export const COUNTRIES = {
    // #region Africa
    "angola": {
        "flag": "🇦🇴",
        "aliases": ["angola", "ao", "🇦🇴"]
    },
    "burkina faso": {
        "flag": "🇧🇫",
        "aliases": ["burkina faso", "bf", "🇧🇫"]
    },
    "burundi": {
        "flag": "🇧🇮",
        "aliases": ["burundi", "bi", "🇧🇮"]
    },
    "benin": {
        "flag": "🇧🇯",
        "aliases": ["benin", "bj", "🇧🇯"]
    },
    "botswana": {
        "flag": "🇧🇼",
        "aliases": ["botswana", "bw", "🇧🇼"]
    },
    "democratic republic of the congo": {
        "flag": "🇨🇩",
        "aliases": ["democratic republic of the congo", "drc", "dr congo", "congo kinshasa", "🇨🇩"]
    },
    "central african republic": {
        "flag": "🇨🇫",
        "aliases": ["central african republic", "car", "cf", "🇨🇫"]
    },
    "republic of the congo": {
        "flag": "🇨🇬",
        "aliases": ["republic of the congo", "congo", "congo brazzaville", "cg", "🇨🇬"]
    },
    "ivory coast": {
        "flag": "🇨🇮",
        "aliases": ["ivory coast", "cote d'ivoire", "côte d'ivoire", "ci", "🇨🇮"]
    },
    "cameroon": {
        "flag": "🇨🇲",
        "aliases": ["cameroon", "cm", "🇨🇲"]
    },
    "cape verde": {
        "flag": "🇨🇻",
        "aliases": ["cape verde", "cv", "capo verde", "🇨🇻"]
    },
    "djibouti": {
        "flag": "🇩🇯",
        "aliases": ["djibouti", "dj", "🇩🇯"]
    },
    "algeria": {
        "flag": "🇩🇿",
        "aliases": ["algeria", "dz", "🇩🇿"]
    },
    "egypt": {
        "flag": "🇪🇬",
        "aliases": ["egypt", "eg", "🇪🇬"]
    },
    "western sahara": {
        "flag": "🇪🇭",
        "aliases": ["western sahara", "eh", "🇪🇭"]
    },
    "eritrea": {
        "flag": "🇪🇷",
        "aliases": ["eritrea", "er", "🇪🇷"]
    },
    "ethiopia": {
        "flag": "🇪🇹",
        "aliases": ["ethiopia", "et", "🇪🇹"]
    },
    "gabon": {
        "flag": "🇬🇦",
        "aliases": ["gabon", "ga", "🇬🇦"]
    },
    "ghana": {
        "flag": "🇬🇭",
        "aliases": ["ghana", "gh", "🇬🇭"]
    },
    "gambia": {
        "flag": "🇬🇲",
        "aliases": ["gambia", "gm", "🇬🇲"]
    },
    "guinea": {
        "flag": "🇬🇳",
        "aliases": ["guinea", "gn", "🇬🇳"]
    },
    "equatorial guinea": {
        "flag": "🇬🇶",
        "aliases": ["equatorial guinea", "gq", "🇬🇶"]
    },
    "guinea-bissau": {
        "flag": "🇬🇼",
        "aliases": ["guinea-bissau", "gw", "guinea bissau", "🇬🇼"]
    },
    "kenya": {
        "flag": "🇰🇪",
        "aliases": ["kenya", "ke", "🇰🇪"]
    },
    "comoros": {
        "flag": "🇰🇲",
        "aliases": ["comoros", "km", "🇰🇲"]
    },
    "liberia": {
        "flag": "🇱🇷",
        "aliases": ["liberia", "lr", "🇱🇷"]
    },
    "lesotho": {
        "flag": "🇱🇸",
        "aliases": ["lesotho", "ls", "🇱🇸"]
    },
    "libya": {
        "flag": "🇱🇾",
        "aliases": ["libya", "ly", "🇱🇾"]
    },
    "morocco": {
        "flag": "🇲🇦",
        "aliases": ["morocco", "ma", "morroco", "🇲🇦"]
    },
    "madagascar": {
        "flag": "🇲🇬",
        "aliases": ["madagascar", "mg", "🇲🇬"]
    },
    "mali": {
        "flag": "🇲🇱",
        "aliases": ["mali", "ml", "🇲🇱"]
    },
    "mauritania": {
        "flag": "🇲🇷",
        "aliases": ["mauritania", "mr", "🇲🇷"]
    },
    "mauritius": {
        "flag": "🇲🇺",
        "aliases": ["mauritius", "mu", "🇲🇺"]
    },
    "malawi": {
        "flag": "🇲🇼",
        "aliases": ["malawi", "mw", "🇲🇼"]
    },
    "mozambique": {
        "flag": "🇲🇿",
        "aliases": ["mozambique", "mz", "🇲🇿"]
    },
    "namibia": {
        "flag": "🇳🇦",
        "aliases": ["namibia", "na", "🇳🇦"]
    },
    "niger": {
        "flag": "🇳🇪",
        "aliases": ["niger", "ne", "🇳🇪"]
    },
    "nigeria": {
        "flag": "🇳🇬",
        "aliases": ["nigeria", "ng", "🇳🇬"]
    },
    "rwanda": {
        "flag": "🇷🇼",
        "aliases": ["rwanda", "rw", "🇷🇼"]
    },
    "seychelles": {
        "flag": "🇸🇨",
        "aliases": ["seychelles", "sc", "🇸🇨"]
    },
    "sudan": {
        "flag": "🇸🇩",
        "aliases": ["sudan", "sd", "🇸🇩"]
    },
    "sierra leone": {
        "flag": "🇸🇱",
        "aliases": ["sierra leone", "sl", "🇸🇱"]
    },
    "senegal": {
        "flag": "🇸🇳",
        "aliases": ["senegal", "sn", "🇸🇳"]
    },
    "somalia": {
        "flag": "🇸🇴",
        "aliases": ["somalia", "so", "🇸🇴"]
    },
    "south sudan": {
        "flag": "🇸🇸",
        "aliases": ["south sudan", "ss", "🇸🇸"]
    },
    "eswatini": {
        "flag": "🇸🇿",
        "aliases": ["eswatini", "swaziland", "sz", "🇸🇿"]
    },
    "chad": {
        "flag": "🇹🇩",
        "aliases": ["chad", "td", "🇹🇩"]
    },
    "togo": {
        "flag": "🇹🇬",
        "aliases": ["togo", "tg", "🇹🇬"]
    },
    "tunisia": {
        "flag": "🇹🇳",
        "aliases": ["tunisia", "tn", "🇹🇳"]
    },
    "tanzania": {
        "flag": "🇹🇿",
        "aliases": ["tanzania", "tz", "🇹🇿"]
    },
    "uganda": {
        "flag": "🇺🇬",
        "aliases": ["uganda", "ug", "🇺🇬"]
    },
    "south africa": {
        "flag": "🇿🇦",
        "aliases": ["south africa", "za", "🇿🇦"]
    },
    "zambia": {
        "flag": "🇿🇲",
        "aliases": ["zambia", "zm", "🇿🇲"]
    },
    "zimbabwe": {
        "flag": "🇿🇼",
        "aliases": ["zimbabwe", "zw", "🇿🇼"]
    },
    // #endregion
    // #region Europe
    "andorra": {
        "flag": "🇦🇩",
        "aliases": ["andorra", "ad", "🇦🇩"]
    },
    "albania": {
        "flag": "🇦🇱",
        "aliases": ["albania", "al", "🇦🇱"]
    },
    "armenia": {
        "flag": "🇦🇲",
        "aliases": ["armenia", "am", "🇦🇲"]
    },
    "austria": {
        "flag": "🇦🇹",
        "aliases": ["austria", "at", "🇦🇹"]
    },
    "bosnia and herzegovina": {
        "flag": "🇧🇦",
        "aliases": ["bosnia and herzegovina", "ba", "bosnia", "🇧🇦"]
    },
    "belgium": {
        "flag": "🇧🇪",
        "aliases": ["belgium", "be", "🇧🇪"]
    },
    "bulgaria": {
        "flag": "🇧🇬",
        "aliases": ["bulgaria", "bg", "🇧🇬"]
    },
    "belarus": {
        "flag": "🇧🇾",
        "aliases": ["belarus", "by", "🇧🇾"]
    },
    "switzerland": {
        "flag": "🇨🇭",
        "aliases": ["switzerland", "ch", "🇨🇭"]
    },
    "cyprus": {
        "flag": "🇨🇾",
        "aliases": ["cyprus", "cy", "🇨🇾"]
    },
    "czech republic": {
        "flag": "🇨🇿",
        "aliases": ["czech republic", "cz", "czechia", "czech", "🇨🇿"]
    },
    "germany": {
        "flag": "🇩🇪",
        "aliases": ["germany", "de", "🇩🇪"]
    },
    "denmark": {
        "flag": "🇩🇰",
        "aliases": ["denmark", "dk", "🇩🇰"]
    },
    "Ceuta & Melilla": {
        "flag": "🇪🇦",
        "aliases": ["Ceuta & Melilla", "ceuta", "ea", "🇪🇦", "ceuta and melilla"]
    },
    "estonia": {
        "flag": "🇪🇪",
        "aliases": ["estonia", "ee", "🇪🇪"]
    },
    "spain": {
        "flag": "🇪🇸",
        "aliases": ["spain", "es", "🇪🇸"]
    },
    "finland": {
        "flag": "🇫🇮",
        "aliases": ["finland", "fi", "🇫🇮"]
    },
    "france": {
        "flag": "🇫🇷",
        "aliases": ["france", "fr", "🇫🇷"]
    },
    "Northern Ireland": {
        "flag": "🇬🇧",
        "aliases": ["Northern Ireland", "🇬🇧", "nir", "NIR"]
    },
    "georgia": {
        "flag": "🇬🇪",
        "aliases": ["georgia", "ge", "🇬🇪"]
    },
    "guernsey": {
        "flag": "🇬🇬",
        "aliases": ["guernsey", "gg", "🇬🇬"]
    },
    "gibraltar": {
        "flag": "🇬🇮",
        "aliases": ["gibraltar", "gi", "🇬🇮"]
    },
    "greece": {
        "flag": "🇬🇷",
        "aliases": ["greece", "gr", "🇬🇷"]
    },
    "croatia": {
        "flag": "🇭🇷",
        "aliases": ["croatia", "hr", "🇭🇷"]
    },
    "hungary": {
        "flag": "🇭🇺",
        "aliases": ["hungary", "hu", "🇭🇺"]
    },
    "ireland": {
        "flag": "🇮🇪",
        "aliases": ["ireland", "ie", "🇮🇪"]
    },
    "isle of man": {
        "flag": "🇮🇲",
        "aliases": ["isle of man", "im", "🇮🇲"]
    },
    "iceland": {
        "flag": "🇮🇸",
        "aliases": ["iceland", "is", "🇮🇸"]
    },
    "italy": {
        "flag": "🇮🇹",
        "aliases": ["italy", "it", "🇮🇹"]
    },
    "jersey": {
        "flag": "🇯🇪",
        "aliases": ["jersey", "je", "🇯🇪"]
    },
    "liechtenstein": {
        "flag": "🇱🇮",
        "aliases": ["liechtenstein", "li", "liech", "🇱🇮"]
    },
    "lithuania": {
        "flag": "🇱🇹",
        "aliases": ["lithuania", "lt", "🇱🇹"]
    },
    "luxembourg": {
        "flag": "🇱🇺",
        "aliases": ["luxembourg", "lu", "🇱🇺"]
    },
    "latvia": {
        "flag": "🇱🇻",
        "aliases": ["latvia", "lv", "🇱🇻"]
    },
    "monaco": {
        "flag": "🇲🇨",
        "aliases": ["monaco", "mc", "🇲🇨"]
    },
    "moldova": {
        "flag": "🇲🇩",
        "aliases": ["moldova", "md", "🇲🇩"]
    },
    "montenegro": {
        "flag": "🇲🇪",
        "aliases": ["montenegro", "me", "🇲🇪"]
    },
    "macedonia": {
        "flag": "🇲🇰",
        "aliases": ["macedonia", "mk", "🇲🇰"]
    },
    "malta": {
        "flag": "🇲🇹",
        "aliases": ["malta", "mt", "🇲🇹"]
    },
    "netherlands": {
        "flag": "🇳🇱",
        "aliases": ["netherlands", "nl", "🇳🇱"]
    },
    "norway": {
        "flag": "🇳🇴",
        "aliases": ["norway", "no", "🇳🇴"]
    },
    "poland": {
        "flag": "🇵🇱",
        "aliases": ["poland", "pl", "🇵🇱"]
    },
    "portugal": {
        "flag": "🇵🇹",
        "aliases": ["portugal", "pt", "🇵🇹"]
    },
    "romania": {
        "flag": "🇷🇴",
        "aliases": ["romania", "ro", "🇷🇴"]
    },
    "serbia": {
        "flag": "🇷🇸",
        "aliases": ["serbia", "rs", "🇷🇸"]
    },
    "russia": {
        "flag": "🇷🇺",
        "aliases": ["russia", "ru", "🇷🇺"]
    },
    "sweden": {
        "flag": "🇸🇪",
        "aliases": ["sweden", "se", "🇸🇪"]
    },
    "slovenia": {
        "flag": "🇸🇮",
        "aliases": ["slovenia", "si", "🇸🇮"]
    },
    "slovakia": {
        "flag": "🇸🇰",
        "aliases": ["slovakia", "sk", "🇸🇰"]
    },
    "san marino": {
        "flag": "🇸🇲",
        "aliases": ["san marino", "sm", "🇸🇲"]
    },
    "ukraine": {
        "flag": "🇺🇦",
        "aliases": ["ukraine", "ua", "🇺🇦"]
    },
    "vatican city": {
        "flag": "🇻🇦",
        "aliases": ["vatican city", "va", "vatican", "🇻🇦"]
    },
    "kosovo": {
        "flag": "🇽🇰",
        "aliases": ["kosovo", "xk", "🇽🇰"]
    },
    "scotland": {
        "flag": ":scotland:",
        "aliases": ["scotland", "sct", ":scotland:"]
    },
    "england": {
        "flag": ":england:",
        "aliases": ["england", "eng", ":england:"]
    },
    "wales": {
        "flag": ":wales:",
        "aliases": ["wales", "wls", ":wales:"]
    },
    "united kingdom": {
        "flag": ":england:",
        "aliases": ["united kingdom", "united kingdom", "uk"]
    },
    // #endregion
    // #region Americas
    "antigua and barbuda": {
        "flag": "🇦🇬",
        "aliases": ["antigua and barbuda", "ag", "🇦🇬"]
    },
    "anguilla": {
        "flag": "🇦🇮",
        "aliases": ["anguilla", "ai", "🇦🇮"]
    },
    "argentina": {
        "flag": "🇦🇷",
        "aliases": ["argentina", "ar", "🇦🇷"]
    },
    "aruba": {
        "flag": "🇦🇼",
        "aliases": ["aruba", "aw", "🇦🇼"]
    },
    "barbados": {
        "flag": "🇧🇧",
        "aliases": ["barbados", "bb", "🇧🇧"]
    },
    "saint barthélemy": {
        "flag": "🇧🇱",
        "aliases": ["saint barthélemy", "bl", "saint barthelemy", "🇧🇱"]
    },
    "bermuda": {
        "flag": "🇧🇲",
        "aliases": ["bermuda", "bm", "🇧🇲"]
    },
    "bolivia": {
        "flag": "🇧🇴",
        "aliases": ["bolivia", "bo", "🇧🇴"]
    },
    "caribbean netherlands": {
        "flag": "🇧🇶",
        "aliases": ["caribbean netherlands", "bq", "🇧🇶"]
    },
    "brazil": {
        "flag": "🇧🇷",
        "aliases": ["brazil", "br", "🇧🇷"]
    },
    "bahamas": {
        "flag": "🇧🇸",
        "aliases": ["bahamas", "bs", "🇧🇸"]
    },
    "belize": {
        "flag": "🇧🇿",
        "aliases": ["belize", "bz", "🇧🇿"]
    },
    "canada": {
        "flag": "🇨🇦",
        "aliases": ["canada", "ca", "🇨🇦"]
    },
    "chile": {
        "flag": "🇨🇱",
        "aliases": ["chile", "cl", "🇨🇱"]
    },
    "colombia": {
        "flag": "🇨🇴",
        "aliases": ["colombia", "co", "🇨🇴"]
    },
    "costa rica": {
        "flag": "🇨🇷",
        "aliases": ["costa rica", "cr", "🇨🇷"]
    },
    "cuba": {
        "flag": "🇨🇺",
        "aliases": ["cuba", "cu", "🇨🇺"]
    },
    "curaçao": {
        "flag": "🇨🇼",
        "aliases": ["curaçao", "cw", "curacao", "🇨🇼"]
    },
    "dominica": {
        "flag": "🇩🇲",
        "aliases": ["dominica", "dm", "🇩🇲"]
    },
    "dominican republic": {
        "flag": "🇩🇴",
        "aliases": ["dominican republic", "do", "🇩🇴"]
    },
    "ecuador": {
        "flag": "🇪🇨",
        "aliases": ["ecuador", "ec", "🇪🇨"]
    },
    "falkland islands": {
        "flag": "🇫🇰",
        "aliases": ["falkland islands", "fk", "🇫🇰"]
    },
    "grenada": {
        "flag": "🇬🇩",
        "aliases": ["grenada", "gd", "🇬🇩"]
    },
    "french guiana": {
        "flag": "🇬🇫",
        "aliases": ["french guiana", "gf", "🇬🇫"]
    },
    "guadeloupe": {
        "flag": "🇬🇵",
        "aliases": ["guadeloupe", "gp", "🇬🇵"]
    },
    "guatemala": {
        "flag": "🇬🇹",
        "aliases": ["guatemala", "gt", "🇬🇹"]
    },
    "guyana": {
        "flag": "🇬🇾",
        "aliases": ["guyana", "gy", "🇬🇾"]
    },
    "honduras": {
        "flag": "🇭🇳",
        "aliases": ["honduras", "hn", '🇭🇳']
    },
    "haiti": {
        "flag": "🇭🇹",
        "aliases": ["haiti", "ht", "🇭🇹"]
    },
    "jamaica": {
        "flag": "🇯🇲",
        "aliases": ["jamaica", "jm", "🇯🇲"]
    },
    "saint kitts and nevis": {
        "flag": "🇰🇳",
        "aliases": ["saint kitts and nevis", "kn", "st kitts", "saint kitts", "saint kitts and nevis", "st kitts and nevis", "🇰🇳"]
    },
    "cayman islands": {
        "flag": "🇰🇾",
        "aliases": ["cayman islands", "ky", "🇰🇾"]
    },
    "saint lucia": {
        "flag": "🇱🇨",
        "aliases": ["saint lucia", "lc", "sainte lucia", "ste lucia", "🇱🇨"]
    },
    "saint martin": {
        "flag": "🇲🇫",
        "aliases": ["saint martin", "mf", "🇲🇫"]
    },
    "martinique": {
        "flag": "🇲🇶",
        "aliases": ["martinique", "mq", "🇲🇶"]
    },
    "montserrat": {
        "flag": "🇲🇸",
        "aliases": ["montserrat", "ms", "🇲🇸"]
    },
    "mexico": {
        "flag": "🇲🇽",
        "aliases": ["mexico", "mx", "🇲🇽"]
    },
    "nicaragua": {
        "flag": "🇳🇮",
        "aliases": ["nicaragua", "ni", "🇳🇮"]
    },
    "panama": {
        "flag": "🇵🇦",
        "aliases": ["panama", "pa", "🇵🇦"]
    },
    "peru": {
        "flag": "🇵🇪",
        "aliases": ["peru", "pe", "🇵🇪"]
    },
    "saint pierre and miquelon": {
        "flag": "🇵🇲",
        "aliases": ["saint pierre and miquelon", "pm", "🇵🇲"]
    },
    "puerto rico": {
        "flag": "🇵🇷",
        "aliases": ["puerto rico", "pr", "🇵🇷"]
    },
    "paraguay": {
        "flag": "🇵🇾",
        "aliases": ["paraguay", "py", "🇵🇾"]
    },
    "suriname": {
        "flag": "🇸🇷",
        "aliases": ["suriname", "sr", "🇸🇷"]
    },
    "el salvador": {
        "flag": "🇸🇻",
        "aliases": ["el salvador", "sv", "salvador", "🇸🇻"]
    },
    "sint maarten": {
        "flag": "🇸🇽",
        "aliases": ["sint maarten", "sx", "🇸🇽"]
    },
    "turks and caicos islands": {
        "flag": "🇹🇨",
        "aliases": ["turks and caicos islands", "tc", "🇹🇨"]
    },
    "trinidad and tobago": {
        "flag": "🇹🇹",
        "aliases": ["trinidad and tobago", "tt", "🇹🇹"]
    },
    "united states": {
        "flag": "🇺🇸",
        "aliases": ["united states", "usa", "us", "🇺🇸"]
    },
    "uruguay": {
        "flag": "🇺🇾",
        "aliases": ["uruguay", "uy", "🇺🇾"]
    },
    "venezuela": {
        "flag": "🇻🇪",
        "aliases": ["venezuela", "ve", "🇻🇪"]
    },
    "british virgin islands": {
        "flag": "🇻🇬",
        "aliases": ["british virgin islands", "vg", "🇻🇬"]
    },
    "us virgin islands": {
        "flag": "🇻🇮",
        "aliases": ["us virgin islands", "vi", "usvi", "🇻🇮"]
    },
    // #endregion
    // #region Asia
    "united arab emirates": {
        "flag": "🇦🇪",
        "aliases": ["united arab emirates", "ae", "uae", "🇦🇪"]
    },
    "afghanistan": {
        "flag": "🇦🇫",
        "aliases": ["afghanistan", "af", "🇦🇫"]
    },
    "azerbaijan": {
        "flag": "🇦🇿",
        "aliases": ["azerbaijan", "az", "🇦🇿"]
    },
    "bangladesh": {
        "flag": "🇧🇩",
        "aliases": ["bangladesh", "bd", "🇧🇩"]
    },
    "bahrain": {
        "flag": "🇧🇭",
        "aliases": ["bahrain", "bh", "🇧🇭"]
    },
    "brunei": {
        "flag": "🇧🇳",
        "aliases": ["brunei", "bn", "🇧🇳"]
    },
    "bhutan": {
        "flag": "🇧🇹",
        "aliases": ["bhutan", "bt", "🇧🇹"]
    },
    "china": {
        "flag": "🇨🇳",
        "aliases": ["china", "cn", "🇨🇳"]
    },
    "hong kong": {
        "flag": "🇭🇰",
        "aliases": ["hong kong", "hk", "🇭🇰"]
    },
    "indonesia": {
        "flag": "🇮🇩",
        "aliases": ["indonesia", "id", "🇮🇩"]
    },
    "israel": {
        "flag": "🇮🇱",
        "aliases": ["israel", "il", "🇮🇱"]
    },
    "india": {
        "flag": "🇮🇳",
        "aliases": ["india", "in", "🇮🇳"]
    },
    "iraq": {
        "flag": "🇮🇶",
        "aliases": ["iraq", "iq", "🇮🇶"]
    },
    "iran": {
        "flag": "🇮🇷",
        "aliases": ["iran", "ir", "🇮🇷"]
    },
    "jordan": {
        "flag": "🇯🇴",
        "aliases": ["jordan", "jo", "🇯🇴"]
    },
    "japan": {
        "flag": "🇯🇵",
        "aliases": ["japan", "jp", "🇯🇵"]
    },
    "kyrgyzstan": {
        "flag": "🇰🇬",
        "aliases": ["kyrgyzstan", "kg", "🇰🇬"]
    },
    "cambodia": {
        "flag": "🇰🇭",
        "aliases": ["cambodia", "kh", "🇰🇭"]
    },
    "north korea": {
        "flag": "🇰🇵",
        "aliases": ["north korea", "kp", "🇰🇵"]
    },
    "south korea": {
        "flag": "🇰🇷",
        "aliases": ["south korea", "kr", "🇰🇷"]
    },
    "kuwait": {
        "flag": "🇰🇼",
        "aliases": ["kuwait", "kw", "🇰🇼"]
    },
    "kazakhstan": {
        "flag": "🇰🇿",
        "aliases": ["kazakhstan", "kz", "🇰🇿"]
    },
    "laos": {
        "flag": "🇱🇦",
        "aliases": ["laos", "la", "🇱🇦"]
    },
    "lebanon": {
        "flag": "🇱🇧",
        "aliases": ["lebanon", "lb", "🇱🇧"]
    },
    "sri lanka": {
        "flag": "🇱🇰",
        "aliases": ["sri lanka", "lk", "🇱🇰"]
    },
    "myanmar": {
        "flag": "🇲🇲",
        "aliases": ["myanmar", "mm", "🇲🇲"]
    },
    "mongolia": {
        "flag": "🇲🇳",
        "aliases": ["mongolia", "mn", "🇲🇳"]
    },
    "macau": {
        "flag": "🇲🇴",
        "aliases": ["macau", "mo", "🇲🇴"]
    },
    "maldives": {
        "flag": "🇲🇻",
        "aliases": ["maldives", "mv", "🇲🇻"]
    },
    "malaysia": {
        "flag": "🇲🇾",
        "aliases": ["malaysia", "my", "🇲🇾"]
    },
    "nepal": {
        "flag": "🇳🇵",
        "aliases": ["nepal", "np", "🇳🇵"]
    },
    "oman": {
        "flag": "🇴🇲",
        "aliases": ["oman", "om", "🇴🇲"]
    },
    "philippines": {
        "flag": "🇵🇭",
        "aliases": ["philippines", "ph", "🇵🇭"]
    },
    "pakistan": {
        "flag": "🇵🇰",
        "aliases": ["pakistan", "pk", "🇵🇰"]
    },
    "palestine": {
        "flag": "🇵🇸",
        "aliases": ["palestine", "ps", "🇵🇸"]
    },
    "qatar": {
        "flag": "🇶🇦",
        "aliases": ["qatar", "qa", "🇶🇦"]
    },
    "russia": {
        "flag": "🇷🇺",
        "aliases": ["russia", "ru", "🇷🇺"]
    },
    "saudi arabia": {
        "flag": "🇸🇦",
        "aliases": ["saudi arabia", "sa", "🇸🇦"]
    },
    "singapore": {
        "flag": "🇸🇬",
        "aliases": ["singapore", "sg", "🇸🇬"]
    },
    "syria": {
        "flag": "🇸🇾",
        "aliases": ["syria", "sy", "🇸🇾"]
    },
    "thailand": {
        "flag": "🇹🇭",
        "aliases": ["thailand", "th", "🇹🇭"]
    },
    "tajikistan": {
        "flag": "🇹🇯",
        "aliases": ["tajikistan", "tj", "🇹🇯"]
    },
    "timor-leste": {
        "flag": "🇹🇱",
        "aliases": ["timor-leste", "tl", "🇹🇱", "timor", "timor leste"]
    },
    "turkmenistan": {
        "flag": "🇹🇲",
        "aliases": ["turkmenistan", "tm", "🇹🇲"]
    },
    "turkey": {
        "flag": "🇹🇷",
        "aliases": ["turkey", "tr", "turkiye", "🇹🇷"]
    },
    "taiwan": {
        "flag": "🇹🇼",
        "aliases": ["taiwan", "tw", "🇹🇼"]
    },
    "uzbekistan": {
        "flag": "🇺🇿",
        "aliases": ["uzbekistan", "uz", "🇺🇿"]
    },
    "vietnam": {
        "flag": "🇻🇳",
        "aliases": ["vietnam", "vn", "🇻🇳"]
    },
    "yemen": {
        "flag": "🇾🇪",
        "aliases": ["yemen", "ye", "🇾🇪"]
    },
    // #endregion
    // #region Oceania
    "ascension island": {
        "flag": "🇦🇨",
        "aliases": ["ascension island", "ac", "🇦🇨", "ascension"]
    },
    "antarctica": {
        "flag": "🇦🇶",
        "aliases": ["antarctica", "aq", "🇦🇶"]
    },
    "american samoa": {
        "flag": "🇦🇸",
        "aliases": ["american samoa", "as", "🇦🇸"]
    },
    "australia": {
        "flag": "🇦🇺",
        "aliases": ["australia", "au", "🇦🇺"]
    },
    "aland islands": {
        "flag": "🇦🇽",
        "aliases": ["aland islands", "ax", "🇦🇽", "aaland", "aland", "aaland islands"]
    },
    "bouvet island": {
        "flag": "🇧🇻",
        "aliases": ["bouvet island", "bv", "🇧🇻", "bouvet"]
    },
    "cocos islands": {
        "flag": "🇨🇨",
        "aliases": ["cocos islands", "cc", "🇨🇨", "cocos"]
    },
    "cook islands": {
        "flag": "🇨🇰",
        "aliases": ["cook islands", "ck", "🇨🇰", "cook"]
    },
    "clipperton island": {
        "flag": "🇨🇵",
        "aliases": ["clipperton", "clipperton island", "cp", "🇨🇵"]
    },
    "christmas island": {
        "flag": "🇨🇽",
        "aliases": ["christmas island", "cx", "🇨🇽"]
    },
    "diego garcia": {
        "flag": "🇩🇬",
        "aliases": ["diego garcia", "dg", "🇩🇬"]
    },
    "fiji": {
        "flag": "🇫🇯",
        "aliases": ["fiji", "fj", "🇫🇯"]
    },
    "micronesia": {
        "flag": "🇫🇲",
        "aliases": ["micronesia", "fm", "🇫🇲"]
    },
    "greenland": {
        "flag": "🇬🇱",
        "aliases": ["greenland", "gl", "🇬🇱"]
    },
    "south georgia and south sandwich islands": {
        "flag": "🇬🇸",
        "aliases": ["south georgia and south sandwich islands", "gs", "🇬🇸", "south georgia"]
    },
    "guam": {
        "flag": "🇬🇺",
        "aliases": ["guam", "gu", "🇬🇺"]
    },
    "heard island and mcdonald islands": {
        "flag": "🇭🇲",
        "aliases": ["heard island and mcdonald islands", "hm", "🇭🇲", "heard island"]
    },
    "canary islands": {
        "flag": "🇮🇨",
        "aliases": ["canary islands", "ic", "🇮🇨", "canary"]
    },
    "british indian ocean territory": {
        "flag": "🇮🇴",
        "aliases": ["british indian ocean territory", "io", "🇮🇴"]
    },
    "kiribati": {
        "flag": "🇰🇮",
        "aliases": ["kiribati", "ki", "🇰🇮"]
    },
    "marshall islands": {
        "flag": "🇲🇭",
        "aliases": ["marshall islands", "mh", "🇲🇭", 'marshall']
    },
    "northern mariana islands": {
        "flag": "🇲🇵",
        "aliases": ["northern mariana islands", "mp", "🇲🇵", "nmi"]
    },
    "new caledonia": {
        "flag": "🇳🇨",
        "aliases": ["new caledonia", "nc", "🇳🇨"]
    },
    "norfolk island": {
        "flag": "🇳🇫",
        "aliases": ["norfolk island", "nf", "🇳🇫", "norfolk"]
    },
    "nauru": {
        "flag": "🇳🇷",
        "aliases": ["nauru", "nr", "🇳🇷"]
    },
    "niue": {
        "flag": "🇳🇺",
        "aliases": ["niue", "nu", "🇳🇺"]
    },
    "new zealand": {
        "flag": "🇳🇿",
        "aliases": ["new zealand", "nz", "🇳🇿"]
    },
    "french polynesia": {
        "flag": "🇵🇫",
        "aliases": ["french polynesia", "pf", "🇵🇫"]
    },
    "papua new guinea": {
        "flag": "🇵🇬",
        "aliases": ["papua new guinea", "pg", "🇵🇬", "png"]
    },
    "pitcairn islands": {
        "flag": "🇵🇳",
        "aliases": ["pitcairn islands", "pn", "🇵🇳"]
    },
    "palau": {
        "flag": "🇵🇼",
        "aliases": ["palau", "pw", "🇵🇼"]
    },
    "reunion": {
        "flag": "🇷🇪",
        "aliases": ["reunion", "re", "🇷🇪"]
    },
    "solomon islands": {
        "flag": "🇸🇧",
        "aliases": ["solomon islands", "sb", "🇸🇧", "solomon"]
    },
    "saint helena": {
        "flag": "🇸🇭",
        "aliases": ["saint helena", "sh", "🇸🇭"]
    },
    "svalbard and jan mayen": {
        "flag": "🇸🇯",
        "aliases": ["svalbard and jan mayen", "sj", "🇸🇯", "svalbard"]
    },
    "sao tome and principe": {
        "flag": "🇸🇹",
        "aliases": ["sao tome and principe", "st", "🇸🇹"]
    },
    "tristan da cunha": {
        "flag": "🇹🇦",
        "aliases": ["tristan da cunha", "ta", "🇹🇦"]
    },
    "french southern territories": {
        "flag": "🇹🇫",
        "aliases": ["french southern territories", "tf", "🇹🇫", "taaf"]
    },
    "tokelau": {
        "flag": "🇹🇰",
        "aliases": ["tokelau", "tk", "🇹🇰"]
    },
    "tonga": {
        "flag": "🇹🇴",
        "aliases": ["tonga", "to", "🇹🇴"]
    },
    "tuvalu": {
        "flag": "🇹🇻",
        "aliases": ["tuvalu", "tv", "🇹🇻"]
    },
    "US Outlying Islands": {
        "flag": "🇺🇲",
        "aliases": ["US Outlying Islands", "Outlying Islands", "um", "🇺🇲", "united states Outlying Islands"]
    },
    "saint vincent and the grenadines": {
        "flag": "🇻🇨",
        "aliases": ["saint vincent and the grenadines", "vc", "saint vincent", "🇻🇨"]
    },
    "vanuatu": {
        "flag": "🇻🇺",
        "aliases": ["vanuatu", "vu", "🇻🇺"]
    },
    "wallis and futuna": {
        "flag": "🇼🇫",
        "aliases": ["wallis and futuna", "wf", "🇼🇫"]
    },
    "samoa": {
        "flag": "🇼🇸",
        "aliases": ["samoa", "ws", "🇼🇸"]
    },
    "mayotte": {
        "flag": "🇾🇹",
        "aliases": ["mayotte", "yt", "🇾🇹"]
    }
    // #endregion
}

export const COUNTRY_LOOKUP = {};
export const COUNTRY_MAPS = {};
Object.keys(COUNTRIES).forEach(country => {
  COUNTRY_LOOKUP[country.toLowerCase()] = country;
  COUNTRIES[country].aliases.forEach(async alias => {
    COUNTRY_LOOKUP[alias.toLowerCase()] = country;

    const domain = alias.toUpperCase();
    if (await fetch('https://api.worldguessr.com/countryLocations/'+domain)) {
      COUNTRIES[country] = domain;
    }
  });
});